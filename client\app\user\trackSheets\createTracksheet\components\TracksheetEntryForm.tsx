"use client";
import React, { useEffect } from "react";
import PageInput from "@/app/_component/PageInput";
import SearchSelect from "@/app/_component/SearchSelect";
import LegrandDetailsComponent from "./LegrandDetailsComponent";
import FormInput from "@/app/_component/FormInput";
import FormDatePicker from "@/app/_component/FormDatePicker";
import FormCheckboxGroup from "@/app/_component/FormCheckboxGroup";
import SingleCheckBox from "@/app/_component/SingleCheckBox";
import { FileText, DollarSign, Info, Hash } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useWarningValidation } from "../hooks/useWarningValidation";
import WarningDisplay from "./WarningDisplay";
import WarningCollapsible from "./WarningCollapsible";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";

interface TracksheetEntryFormProps {
  index: number;
  form: any;
  clientOptions: any[];
  carrierByClient: any[];
  legrandData: any[];
  handleFtpFileNameChange: (index: number, value: string) => void;
  handleLegrandDataChange: (
    entryIndex: number,
    businessUnit: string,
    divisionCode: string
  ) => void;
  getFilteredDivisionOptions: (company: string, entryIndex?: number) => any[];
  updateFilenames: () => void;
  clientFilePathFormat: string | null;
  generatedFilenames: string[];
  filenameValidation: boolean[];
  renderTooltipContent: (index: number) => React.ReactNode;
  checkInvoiceExistence: (invoice: string) => Promise<boolean>;
  checkReceivedDateExistence: (
    invoice: string,
    receivedDate: string
  ) => Promise<boolean>;
  validateDateFormat: (date: string) => boolean;
  handleDateChange: (index: number, value: string) => void;
  legrandsData: any[];
  manualMatchingData: any[];
  handleManualMatchingAutoFill: (entryIndex: number, division: string) => void;
  assignedFiles: any[];
}

const TracksheetEntryForm: React.FC<TracksheetEntryFormProps> = ({
  index,
  form,
  clientOptions,
  carrierByClient,
  legrandData,
  handleFtpFileNameChange,
  handleLegrandDataChange,
  getFilteredDivisionOptions,
  updateFilenames,
  clientFilePathFormat,
  generatedFilenames,
  filenameValidation,
  renderTooltipContent,
  checkInvoiceExistence,
  checkReceivedDateExistence,
  validateDateFormat,
  handleDateChange,
  legrandsData,
  manualMatchingData,
  handleManualMatchingAutoFill,
  assignedFiles,
}) => {
  console.log(`[TracksheetEntryForm] Rendering for index: ${index}`); // Debug log

  const { warnings, validateWarnings } = useWarningValidation();

  // Destructure the specific values that the effect depends on
  const legrandFreightTerms = form.watch(
    `entries.${index}.legrandFreightTerms`
  );
  const shipperType = form.watch(`entries.${index}.shipperType`);
  const consigneeType = form.watch(`entries.${index}.consigneeType`);
  const billtoType = form.watch(`entries.${index}.billtoType`);

  useEffect(() => {
    console.log(
      `[TracksheetEntryForm] Watched values changed for index ${index}:`,
      {
        legrandFreightTerms,
        shipperType,
        consigneeType,
        billtoType,
      }
    );

    // Map frontend values to API values
    const freightTermMap: { [key: string]: string } = {
      Prepaid: "PREPAID",
      Collect: "COLLECT",
      "Third Party Billing": "THIRD_PARTY",
    };

    const apiFreightTerm = freightTermMap[legrandFreightTerms] || "";

    if (apiFreightTerm && shipperType && consigneeType && billtoType) {
      console.log(
        `[TracksheetEntryForm] Calling validateWarnings for index ${index}`
      );
      validateWarnings({
        freightTerm: apiFreightTerm,
        shipperAddressType: shipperType,
        consigneeAddressType: consigneeType,
        billToAddressType: billtoType,
      });
    }
  }, [
    legrandFreightTerms,
    shipperType,
    consigneeType,
    billtoType,
    validateWarnings,
    index,
  ]);

  // --- START: New Warning Distribution Logic ---

  const responsiblePartyMap: { [key: string]: string | null } = {
    Prepaid: "Shipper",
    Collect: "Consignee",
    "Third Party Billing": "Bill-to",
  };
  const responsibleParty = responsiblePartyMap[legrandFreightTerms] || null;

  const getDistributedWarnings = () => {
    if (!warnings || !warnings.success) {
      return {
        shipper: { HIGH: [], MEDIUM: [], CRITICAL: [] },
        consignee: { HIGH: [], MEDIUM: [], CRITICAL: [] },
        billto: { HIGH: [], MEDIUM: [], CRITICAL: [] },
      };
    }

    const { HIGH = [], MEDIUM = [], CRITICAL = [] } = warnings.warnings;

    // Distribute HIGH warnings to the responsible party
    const shipperHigh = responsibleParty === "Shipper" ? HIGH : [];
    const consigneeHigh = responsibleParty === "Consignee" ? HIGH : [];
    const billtoHigh = responsibleParty === "Bill-to" ? HIGH : [];

    // Distribute MEDIUM warnings
    const partyKeywords = ["Shipper", "Consignee", "Bill-to"];
    const generalMedium = MEDIUM.filter(
      (w) => !partyKeywords.some((p) => w.message.includes(p))
    );

    let shipperMedium = MEDIUM.filter((w) => w.message.includes("Shipper"));
    let consigneeMedium = MEDIUM.filter((w) => w.message.includes("Consignee"));
    let billtoMedium = MEDIUM.filter((w) => w.message.includes("Bill-to"));

    // Add general warnings to any party with a CV type
    if (shipperType === "CV") shipperMedium.push(...generalMedium);
    if (consigneeType === "CV") consigneeMedium.push(...generalMedium);
    if (billtoType === "CV") billtoMedium.push(...generalMedium);

    // For now, CRITICAL warnings are not party-specific, so just pass empty arrays
    return {
      shipper: { HIGH: shipperHigh, MEDIUM: shipperMedium, CRITICAL: [] },
      consignee: { HIGH: consigneeHigh, MEDIUM: consigneeMedium, CRITICAL: [] },
      billto: { HIGH: billtoHigh, MEDIUM: billtoMedium, CRITICAL: [] },
    };
  };

  const distributedWarnings = getDistributedWarnings();
  const {
    shipper: shipperWarnings = { HIGH: [], MEDIUM: [], CRITICAL: [] },
    consignee: consigneeWarnings = { HIGH: [], MEDIUM: [], CRITICAL: [] },
    billto: billtoWarnings = { HIGH: [], MEDIUM: [], CRITICAL: [] },
  } = distributedWarnings;

  // --- END: New Warning Distribution Logic ---

  const formValues = form.getValues();
  const entry = formValues.entries?.[index] || {};
  const entryClientId = entry?.clientId || formValues.clientId || "";
  const entryClientName =
    clientOptions?.find((c: any) => c.value === entryClientId)?.name || "";
  const customFields = entry?.customFields || [];

  const handleDcCvToggle = (
    fieldPrefix: "shipper" | "consignee" | "billto",
    newType: "DC" | "CV"
  ) => {
    const currentType = form.getValues(`entries.${index}.${fieldPrefix}Type`);
    if (currentType === newType) return; // No change

    // Clear the Legrand fields for the block
    form.setValue(`entries.${index}.${fieldPrefix}Alias`, "");
    form.setValue(`entries.${index}.${fieldPrefix}Address`, "");
    form.setValue(`entries.${index}.${fieldPrefix}Zipcode`, "");

    // If switching to CV for the responsible block, clear shared fields
    if (newType === "CV") {
      const responsiblePartyMap: { [key: string]: string | null } = {
        Prepaid: "Shipper",
        Collect: "Consignee",
        "Third Party Billing": "Bill-to",
      };
      const blockTypeMap = {
        shipper: "Shipper",
        consignee: "Consignee",
        billto: "Bill-to",
      };
      const currentFreightTerm = form.getValues(
        `entries.${index}.legrandFreightTerms`
      );

      if (
        responsiblePartyMap[currentFreightTerm] === blockTypeMap[fieldPrefix]
      ) {
        form.setValue(`entries.${index}.company`, "");
        form.setValue(`entries.${index}.division`, "");
        form.setValue(`entries.${index}.manualMatching`, "");
      }
    }

    // Set the new DC/CV type
    form.setValue(`entries.${index}.${fieldPrefix}Type`, newType, {
      shouldValidate: true,
    });
  };

  const shipperAlias = form.watch(`entries.${index}.shipperAlias`);
  const shipperAddress = form.watch(`entries.${index}.shipperAddress`);
  const shipperZipcode = form.watch(`entries.${index}.shipperZipcode`);
  const consigneeAlias = form.watch(`entries.${index}.consigneeAlias`);
  const consigneeAddress = form.watch(`entries.${index}.consigneeAddress`);
  const consigneeZipcode = form.watch(`entries.${index}.consigneeZipcode`);
  const billtoAlias = form.watch(`entries.${index}.billtoAlias`);
  const billtoAddress = form.watch(`entries.${index}.billtoAddress`);
  const billtoZipcode = form.watch(`entries.${index}.billtoZipcode`);

  const selectedFreightTerm = form.watch(
    `entries.${index}.legrandFreightTerms`
  );
  const shipperTypeVal = form.watch(`entries.${index}.shipperType`);
  const consigneeTypeVal = form.watch(`entries.${index}.consigneeType`);
  const billtoTypeVal = form.watch(`entries.${index}.billtoType`);

  let isAutoFilled = false;
  if (entryClientName === "LEGRAND") {
    if (selectedFreightTerm === "Prepaid" && shipperTypeVal === "DC") {
      isAutoFilled = !!(shipperAlias || shipperAddress || shipperZipcode);
    } else if (selectedFreightTerm === "Collect" && consigneeTypeVal === "DC") {
      isAutoFilled = !!(consigneeAlias || consigneeAddress || consigneeZipcode);
    } else if (
      selectedFreightTerm === "Third Party Billing" &&
      billtoTypeVal === "DC"
    ) {
      isAutoFilled = !!(billtoAlias || billtoAddress || billtoZipcode);
    }
  }

  let isResponsibleBlockCV = false;
  if (entryClientName === "LEGRAND") {
    if (selectedFreightTerm === "Prepaid" && shipperTypeVal === "CV") {
      isResponsibleBlockCV = true;
    } else if (selectedFreightTerm === "Collect" && consigneeTypeVal === "CV") {
      isResponsibleBlockCV = true;
    } else if (
      selectedFreightTerm === "Third Party Billing" &&
      billtoTypeVal === "CV"
    ) {
      isResponsibleBlockCV = true;
    }
  }

  console.log(
    `[TracksheetEntryForm] Client name for index ${index}: '${entryClientName}'`
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs">
            {index + 1}
          </div>
          <h2 className="text-sm font-semibold text-gray-900">
            Entry #{index + 1}
          </h2>
        </div>
      </div>

      {/* FTP File Name Dropdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-2 mb-4">
        {/* FTP File Name Dropdown */}
        <div>
          <SearchSelect
            form={form}
            name={`entries.${index}.ftpFileName`}
            label="FTP File Name"
            placeholder="Search FTP File Name"
            options={assignedFiles.map((file) => ({
              label: file.fileName,
              value: file.fileName,
            }))}
            isRequired
            onValueChange={(value) => handleFtpFileNameChange(index, value)}
          />
          {assignedFiles.length === 0 && (
            <div className="text-xs text-gray-500 mt-1">
              No files assigned to you.
            </div>
          )}
        </div>
        {/* End FTP File Name Dropdown */}
        <PageInput
          className="mt-2"
          form={form}
          label="FTP Page"
          name={`entries.${index}.ftpPage`}
          isRequired
        />
        <div className="mt-10">
          <SingleCheckBox
            form={form}
            name={`entries.${index}.finalInvoice`}
            label="Is Invoice Final?"
            className="space-x-2"
          />
        </div>
        <SearchSelect
          form={form}
          name={`entries.${index}.carrierName`}
          label="Select Carrier"
          placeholder="Search Carrier"
          options={carrierByClient}
          isRequired
          onValueChange={() => setTimeout(() => updateFilenames(), 100)}
        />
        {/* Radio - Billed to client */}
        <div className="mt-6">
          <label className="text-sm font-medium text-gray-700 mb-1 block">
            Billed to {entryClientName || "Client"}
          </label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                {...form.register(`entries.${index}.billToClient`)}
                value="yes"
                defaultChecked
                className="mr-2"
              />
              <span className="text-sm">Yes</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                {...form.register(`entries.${index}.billToClient`)}
                value="no"
                className="mr-2"
              />
              <span className="text-sm">No</span>
            </label>
          </div>
        </div>
      </div>
      {/* Legrand Section - Restored UI and DC/CV toggles */}
      {entryClientName === "LEGRAND" &&
        (() => {
          const selectedFreightTerm = form.getValues(
            `entries.${index}.legrandFreightTerms`
          );
          const isFreightTermSelected = !!selectedFreightTerm;

          const isShipperDCorCVSelected = !!form.getValues(
            `entries.${index}.shipperType`
          );
          const isConsigneeDCorCVSelected = !!form.getValues(
            `entries.${index}.consigneeType`
          );
          const isBilltoDCorCVSelected = !!form.getValues(
            `entries.${index}.billtoType`
          );
          return (
            <div className="mb-3">
              {form.formState.errors?.entries?.[index]?.legrandFreightTerms && (
                <div className="text-xs text-red-600 mb-2">
                  {
                    form.formState.errors.entries[index].legrandFreightTerms
                      .message
                  }
                </div>
              )}
              {/* Radio buttons for billing type, styled and grouped above each block */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3">
                {/* Prepaid - Shipper */}
                <div>
                  <div className="flex flex-col items-center mb-2">
                    <label
                      htmlFor={`prepaid-${index}`}
                      className={`flex items-center gap-0.5 px-1 py-0.5 rounded-full cursor-pointer transition-all duration-200 select-none bg-white
                      ${
                        selectedFreightTerm === "Prepaid"
                          ? "text-sm text-blue-600"
                          : "text-sm text-gray-700"
                      }
                    `}
                    >
                      <span
                        className={`w-3.5 h-3.5 flex items-center justify-center rounded-full border-2 transition-all duration-200
                        ${
                          selectedFreightTerm === "Prepaid"
                            ? "border-blue-600 bg-blue-600"
                            : "border-gray-300 bg-white"
                        }
                      `}
                      >
                        {selectedFreightTerm === "Prepaid" ? (
                          <svg
                            className="w-3 h-3 text-white"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        ) : null}
                      </span>
                      <span className="font-semibold">Prepaid</span>
                      <input
                        type="radio"
                        id={`prepaid-${index}`}
                        name={`entries.${index}.legrandFreightTerms`}
                        value="Prepaid"
                        checked={selectedFreightTerm === "Prepaid"}
                        onChange={() =>
                          form.setValue(
                            `entries.${index}.legrandFreightTerms`,
                            "Prepaid",
                            { shouldValidate: true }
                          )
                        }
                        className="hidden"
                      />
                    </label>
                  </div>
                  {form.formState.errors?.entries?.[index]?.shipperType && (
                    <div className="text-xs text-red-600 mb-2">
                      {form.formState.errors.entries[index].shipperType.message}
                    </div>
                  )}
                  <div>
                    <LegrandDetailsComponent
                      form={form}
                      entryIndex={index}
                      onLegrandDataChange={handleLegrandDataChange}
                      blockTitle="Shipper"
                      fieldPrefix="shipper"
                      legrandData={legrandData}
                      disableFields={
                        !isFreightTermSelected || !isShipperDCorCVSelected
                      }
                      highlight={selectedFreightTerm === "Prepaid"}
                      disabled={!form.getValues(`entries.${index}.shipperType`)}
                      isCV={
                        form.getValues(`entries.${index}.shipperType`) === "CV"
                      }
                      dcCvToggle={
                        <div className="flex gap-1 mb-1">
                          {["DC", "CV"].map((type) => (
                            <button
                              key={type}
                              type="button"
                              className={`px-2 py-0.5 rounded-full border text-xs font-semibold transition-all duration-150
                              ${
                                form.getValues(
                                  `entries.${index}.shipperType`
                                ) === type
                                  ? "bg-blue-600 text-white border-blue-600"
                                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
                              }
                            `}
                              onClick={() =>
                                handleDcCvToggle("shipper", type as "DC" | "CV")
                              }
                            >
                              {type}
                            </button>
                          ))}
                        </div>
                      }
                      selectedFreightTerm={selectedFreightTerm}
                      blockType="Shipper"
                    />
                    <WarningCollapsible warnings={shipperWarnings} />
                  </div>
                </div>
                {/* Collect - Consignee */}
                <div>
                  <div className="flex flex-col items-center mb-2">
                    <label
                      htmlFor={`collect-${index}`}
                      className={`flex items-center gap-0.5 px-1 py-0.5 rounded-full cursor-pointer transition-all duration-200 select-none bg-white
                      ${
                        selectedFreightTerm === "Collect"
                          ? "text-sm text-blue-600"
                          : "text-sm text-gray-700"
                      }
                    `}
                    >
                      <span
                        className={`w-3.5 h-3.5 flex items-center justify-center rounded-full border-2 transition-all duration-200
                        ${
                          selectedFreightTerm === "Collect"
                            ? "border-blue-600 bg-blue-600"
                            : "border-gray-300 bg-white"
                        }
                      `}
                      >
                        {selectedFreightTerm === "Collect" ? (
                          <svg
                            className="w-3 h-3 text-white"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        ) : null}
                      </span>
                      <span className="font-semibold">Collect</span>
                      <input
                        type="radio"
                        id={`collect-${index}`}
                        name={`entries.${index}.legrandFreightTerms`}
                        value="Collect"
                        checked={selectedFreightTerm === "Collect"}
                        onChange={() =>
                          form.setValue(
                            `entries.${index}.legrandFreightTerms`,
                            "Collect",
                            { shouldValidate: true }
                          )
                        }
                        className="hidden"
                      />
                    </label>
                  </div>
                  {form.formState.errors?.entries?.[index]?.consigneeType && (
                    <div className="text-xs text-red-600 mb-2">
                      {
                        form.formState.errors.entries[index].consigneeType
                          .message
                      }
                    </div>
                  )}
                  <div>
                    <LegrandDetailsComponent
                      form={form}
                      entryIndex={index}
                      onLegrandDataChange={handleLegrandDataChange}
                      blockTitle="Consignee"
                      fieldPrefix="consignee"
                      legrandData={legrandData}
                      disableFields={
                        !isFreightTermSelected || !isConsigneeDCorCVSelected
                      }
                      highlight={selectedFreightTerm === "Collect"}
                      disabled={
                        !form.getValues(`entries.${index}.consigneeType`)
                      }
                      isCV={
                        form.getValues(`entries.${index}.consigneeType`) ===
                        "CV"
                      }
                      dcCvToggle={
                        <div className="flex gap-1 mb-1">
                          {["DC", "CV"].map((type) => (
                            <button
                              key={type}
                              type="button"
                              className={`px-2 py-0.5 rounded-full border text-xs font-semibold transition-all duration-150
                              ${
                                form.getValues(
                                  `entries.${index}.consigneeType`
                                ) === type
                                  ? "bg-blue-600 text-white border-blue-600"
                                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
                              }
                            `}
                              onClick={() =>
                                handleDcCvToggle(
                                  "consignee",
                                  type as "DC" | "CV"
                                )
                              }
                            >
                              {type}
                            </button>
                          ))}
                        </div>
                      }
                      selectedFreightTerm={selectedFreightTerm}
                      blockType="Consignee"
                    />
                    <WarningCollapsible warnings={consigneeWarnings} />
                  </div>
                </div>
                {/* Third Party Billing - Bill-to */}
                <div>
                  <div className="flex flex-col items-center mb-2">
                    <label
                      htmlFor={`thirdparty-${index}`}
                      className={`flex items-center gap-0.5 px-1 py-0.5 rounded-full cursor-pointer transition-all duration-200 select-none bg-white
                      ${
                        selectedFreightTerm === "Third Party Billing"
                          ? "text-sm text-blue-600"
                          : "text-sm text-gray-700"
                      }
                    `}
                    >
                      <span
                        className={`w-3.5 h-3.5 flex items-center justify-center rounded-full border-2 transition-all duration-200
                        ${
                          selectedFreightTerm === "Third Party Billing"
                            ? "border-blue-600 bg-blue-600"
                            : "border-gray-300 bg-white"
                        }
                      `}
                      >
                        {selectedFreightTerm === "Third Party Billing" ? (
                          <svg
                            className="w-3 h-3 text-white"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="3"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        ) : null}
                      </span>
                      <span className="font-semibold">Third Party Billing</span>
                      <input
                        type="radio"
                        id={`thirdparty-${index}`}
                        name={`entries.${index}.legrandFreightTerms`}
                        value="Third Party Billing"
                        checked={selectedFreightTerm === "Third Party Billing"}
                        onChange={() =>
                          form.setValue(
                            `entries.${index}.legrandFreightTerms`,
                            "Third Party Billing",
                            { shouldValidate: true }
                          )
                        }
                        className="hidden"
                      />
                    </label>
                  </div>
                  {form.formState.errors?.entries?.[index]?.billtoType && (
                    <div className="text-xs text-red-600 mb-2">
                      {form.formState.errors.entries[index].billtoType.message}
                    </div>
                  )}
                  <div>
                    <LegrandDetailsComponent
                      form={form}
                      entryIndex={index}
                      onLegrandDataChange={handleLegrandDataChange}
                      blockTitle="Bill-to"
                      fieldPrefix="billto"
                      legrandData={legrandData}
                      disableFields={
                        !isFreightTermSelected || !isBilltoDCorCVSelected
                      }
                      highlight={selectedFreightTerm === "Third Party Billing"}
                      disabled={!form.getValues(`entries.${index}.billtoType`)}
                      isCV={
                        form.getValues(`entries.${index}.billtoType`) === "CV"
                      }
                      dcCvToggle={
                        <div className="flex gap-1 mb-1">
                          {["DC", "CV"].map((type) => (
                            <button
                              key={type}
                              type="button"
                              className={`px-2 py-0.5 rounded-full border text-xs font-semibold transition-all duration-150
                              ${
                                form.getValues(
                                  `entries.${index}.billtoType`
                                ) === type
                                  ? "bg-blue-600 text-white border-blue-600"
                                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-100"
                              }
                            `}
                              onClick={() =>
                                handleDcCvToggle("billto", type as "DC" | "CV")
                              }
                            >
                              {type}
                            </button>
                          ))}
                        </div>
                      }
                      selectedFreightTerm={selectedFreightTerm}
                      blockType="Bill-to"
                    />
                    <WarningCollapsible warnings={billtoWarnings} />
                  </div>
                </div>
              </div>
            </div>
          );
        })()}

      {/* Company, Division, Manual Matching */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-3 mt-4">
        {/* Company Field - Show dropdown when Legrand client and responsible block is CV */}
        {entryClientName === "LEGRAND" && isResponsibleBlockCV ? (
          (() => {
            // Get unique company options
            const uniqueCompanies = Array.from(
              new Set(legrandsData.map((item: any) => item.businessUnit))
            ).filter(Boolean);

            return (
              <SearchSelect
                form={form}
                name={`entries.${index}.company`}
                label="Company"
                placeholder="Select Company"
                isRequired
                options={uniqueCompanies.map((company: string) => ({
                  value: company,
                  label: company,
                }))}
                onValueChange={() => {
                  // Clear division when company changes
                  form.setValue(`entries.${index}.division`, "");
                  form.setValue(`entries.${index}.manualMatching`, "");
                }}
                disabled={false}
              />
            );
          })()
        ) : (
          <FormInput
            form={form}
            label="Company"
            name={`entries.${index}.company`}
            placeholder="Enter Company Name"
            type="text"
            isRequired
            disable
          />
        )}

        {/* Division Field - Show dropdown when Legrand client and responsible block is CV */}
        {entryClientName === "LEGRAND" && isResponsibleBlockCV ? (
          (() => {
            const selectedCompany = form.watch(`entries.${index}.company`);
            const filteredDivisions = legrandsData.filter(
              (item: any) =>
                item.businessUnit === selectedCompany && item.customeCode
            );

            // Split divisions by "/" and get unique division options
            const allDivisions: string[] = [];
            filteredDivisions.forEach((entry) => {
              if (entry.customeCode) {
                if (entry.customeCode.includes("/")) {
                  const splitDivisions = entry.customeCode
                    .split("/")
                    .map((d: string) => d.trim());
                  allDivisions.push(...splitDivisions);
                } else {
                  allDivisions.push(entry.customeCode);
                }
              }
            });

            const uniqueDivisions = Array.from(new Set(allDivisions)).filter(
              Boolean
            );

            return (
              <SearchSelect
                form={form}
                name={`entries.${index}.division`}
                label="Division"
                placeholder="Select Division"
                isRequired
                options={uniqueDivisions.map((division: string) => ({
                  value: division,
                  label: division,
                }))}
                onValueChange={(value) => {
                  // Use the same auto-fill logic as DC
                  if (value) {
                    handleManualMatchingAutoFill(index, value);
                  } else {
                    form.setValue(`entries.${index}.manualMatching`, "");
                  }
                }}
                disabled={!selectedCompany}
              />
            );
          })()
        ) : entryClientName === "LEGRAND" ? (
          (() => {
            const divisionOptions = getFilteredDivisionOptions(
              entry.company,
              index
            );
            if (divisionOptions.length <= 1) {
              // Set the value in the form state if not already set
              if (
                divisionOptions.length === 1 &&
                form.getValues(`entries.${index}.division`) !==
                  divisionOptions[0].value
              ) {
                form.setValue(
                  `entries.${index}.division`,
                  divisionOptions[0].value
                );
              }
              return (
                <FormInput
                  form={form}
                  name={`entries.${index}.division`}
                  label="Division"
                  placeholder="Division"
                  type="text"
                  disable={true} // read-only if only one division
                />
              );
            } else {
              return (
                <SearchSelect
                  form={form}
                  name={`entries.${index}.division`}
                  label="Division"
                  placeholder="Select Division"
                  options={divisionOptions}
                  onValueChange={() => {}}
                />
              );
            }
          })()
        ) : (
          <FormInput
            form={form}
            name={`entries.${index}.division`}
            label="Division"
            placeholder="Enter Division"
            type="text"
          />
        )}

        {/* Manual Matching Field - Show input when Legrand client and responsible block is CV */}
        {entryClientName === "LEGRAND" && isResponsibleBlockCV ? (
          (() => {
            const selectedDivision = form.watch(`entries.${index}.division`);
            const currentManualMatching = form.watch(
              `entries.${index}.manualMatching`
            );

            // Check if manual matching is auto-filled
            const isManualMatchingAutoFilled =
              selectedDivision &&
              currentManualMatching &&
              manualMatchingData.some(
                (item: any) =>
                  item.division === selectedDivision &&
                  item.ManualShipment === currentManualMatching
              );

            return (
              <FormInput
                form={form}
                label="Manual or Matching"
                name={`entries.${index}.manualMatching`}
                type="text"
                isRequired
                disable={isManualMatchingAutoFilled || !selectedDivision}
                placeholder={
                  !selectedDivision
                    ? "Select division first"
                    : "Auto-filled from division"
                }
              />
            );
          })()
        ) : (
          <FormInput
            form={form}
            label="Manual or Matching"
            name={`entries.${index}.manualMatching`}
            type="text"
            isRequired
            disable={isAutoFilled}
          />
        )}
      </div>

      {/* Document Information Section */}
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <FileText className="w-4 h-4 text-orange-600" />
          <h3 className="text-sm font-semibold text-gray-900">
            Document Information
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
          <FormInput
            form={form}
            label="Master Invoice"
            placeholder="Enter master invoice"
            name={`entries.${index}.masterInvoice`}
            type="text"
            onBlur={(e) => {
              const masterValue = e.target.value;
              const currentInvoice = form.getValues(`entries.${index}.invoice`);
              if (masterValue && !currentInvoice) {
                form.setValue(`entries.${index}.invoice`, masterValue);
              }
            }}
          />
          <FormInput
            form={form}
            label="Invoice"
            placeholder="Enter invoice"
            name={`entries.${index}.invoice`}
            type="text"
            isRequired
            onBlur={async (e) => {
              console.log("Invoice onBlur fired", e.target.value);
              const invoiceValue = e.target.value;
              const receivedDate = form.getValues(
                `entries.${index}.receivedDate`
              );
              form.clearErrors(`entries.${index}.invoice`);
              form.clearErrors(`entries.${index}.receivedDate`);
              console.log("invoiceValue:", invoiceValue);
              console.log(
                "invoiceValue.length >= 3:",
                invoiceValue.length >= 3
              );
              console.log(
                "typeof checkInvoiceExistence:",
                typeof checkInvoiceExistence
              );
              console.log(
                "typeof checkReceivedDateExistence:",
                typeof checkReceivedDateExistence
              );
              if (
                invoiceValue &&
                invoiceValue.length >= 3 &&
                typeof checkInvoiceExistence === "function" &&
                typeof checkReceivedDateExistence === "function"
              ) {
                console.log(
                  "About to call checkInvoiceExistence",
                  checkInvoiceExistence,
                  invoiceValue
                );
                const exists = await checkInvoiceExistence(invoiceValue);
                console.log("Invoice exists:", exists);
                if (exists) {
                  form.setError(`entries.${index}.invoice`, {
                    type: "manual",
                    message: "This invoice already exists",
                  });
                  if (receivedDate) {
                    const receivedDateExists = await checkReceivedDateExistence(
                      invoiceValue,
                      receivedDate
                    );
                    if (receivedDateExists) {
                      form.setError(`entries.${index}.receivedDate`, {
                        type: "manual",
                        message:
                          "This received date already exists for this invoice",
                      });
                    } else {
                      form.setError(`entries.${index}.receivedDate`, {
                        type: "manual",
                        message:
                          "Warning: Different received date for existing invoice",
                      });
                    }
                  }
                }
              }
            }}
          />
          <FormInput
            form={form}
            label="BOL"
            placeholder="Enter BOL"
            name={`entries.${index}.bol`}
            type="text"
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-2">
          <FormDatePicker
            form={form}
            label="Received Date"
            name={`entries.${index}.receivedDate`}
            isRequired
            placeholder="DD/MM/YYYY"
            onValueChange={async (value) => {
              form.clearErrors(`entries.${index}.receivedDate`);
              if (typeof handleDateChange === "function")
                handleDateChange(index, value);
              const invoice = form.getValues(`entries.${index}.invoice`);
              if (
                value &&
                invoice &&
                invoice.length >= 3 &&
                typeof checkInvoiceExistence === "function" &&
                typeof checkReceivedDateExistence === "function"
              ) {
                const invoiceExists = await checkInvoiceExistence(invoice);
                if (invoiceExists) {
                  const receivedDateExists = await checkReceivedDateExistence(
                    invoice,
                    value
                  );
                  if (receivedDateExists) {
                    form.setError(`entries.${index}.receivedDate`, {
                      type: "manual",
                      message:
                        "This received date already exists for this invoice",
                    });
                  } else {
                    form.setError(`entries.${index}.receivedDate`, {
                      type: "manual",
                      message:
                        "Warning: Different received date for existing invoice",
                    });
                  }
                }
              }
              // Date relationship check
              const invoiceDate = form.getValues(
                `entries.${index}.invoiceDate`
              );
              form.clearErrors(`entries.${index}.invoiceDate`);
              if (
                invoiceDate &&
                value &&
                typeof validateDateFormat === "function"
              ) {
                if (
                  validateDateFormat(invoiceDate) &&
                  validateDateFormat(value)
                ) {
                  const [invDay, invMonth, invYear] = invoiceDate
                    .split("/")
                    .map(Number);
                  const [recDay, recMonth, recYear] = value
                    .split("/")
                    .map(Number);
                  const invoiceDateObj = new Date(
                    invYear,
                    invMonth - 1,
                    invDay
                  );
                  const receivedDateObj = new Date(
                    recYear,
                    recMonth - 1,
                    recDay
                  );
                  if (invoiceDateObj > receivedDateObj) {
                    form.setError(`entries.${index}.invoiceDate`, {
                      type: "manual",
                      message:
                        "The invoice date should be older than or the same as the received date.",
                    });
                  }
                }
              }
            }}
          />
          <FormDatePicker
            form={form}
            label="Invoice Date"
            name={`entries.${index}.invoiceDate`}
            isRequired
            placeholder="DD/MM/YYYY"
            onValueChange={async (value) => {
              const receivedDate = form.getValues(
                `entries.${index}.receivedDate`
              );
              form.clearErrors(`entries.${index}.invoiceDate`);
              if (
                value &&
                receivedDate &&
                typeof validateDateFormat === "function"
              ) {
                if (
                  validateDateFormat(value) &&
                  validateDateFormat(receivedDate)
                ) {
                  const [invDay, invMonth, invYear] = value
                    .split("/")
                    .map(Number);
                  const [recDay, recMonth, recYear] = receivedDate
                    .split("/")
                    .map(Number);
                  const invoiceDateObj = new Date(
                    invYear,
                    invMonth - 1,
                    invDay
                  );
                  const receivedDateObj = new Date(
                    recYear,
                    recMonth - 1,
                    recDay
                  );
                  if (invoiceDateObj > receivedDateObj) {
                    form.setError(`entries.${index}.invoiceDate`, {
                      type: "manual",
                      message:
                        "The invoice date should be older than or the same as the received date.",
                    });
                  }
                }
              }
            }}
          />
          <FormDatePicker
            form={form}
            label="Shipment Date"
            name={`entries.${index}.shipmentDate`}
            placeholder="DD/MM/YYYY"
          />
        </div>
      </div>

      {/* Financial & Shipment Information Section */}
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <DollarSign className="w-4 h-4 text-green-600" />
          <h3 className="text-sm font-semibold text-gray-900">
            Financial & Shipment
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <FormInput
            className="mt-2"
            form={form}
            label="Invoice Total"
            placeholder="Enter invoice total"
            name={`entries.${index}.invoiceTotal`}
            type="number"
            isRequired
          />
          <SearchSelect
            form={form}
            name={`entries.${index}.currency`}
            label="Currency"
            placeholder="Search currency"
            isRequired
            options={[
              { value: "USD", label: "USD" },
              { value: "CAD", label: "CAD" },
              { value: "EUR", label: "EUR" },
            ]}
          />
          <FormInput
            className="mt-2"
            form={form}
            label="Savings"
            placeholder="Enter savings"
            name={`entries.${index}.savings`}
            type="text"
          />
          <FormInput
            className="mt-2"
            form={form}
            label="Notes"
            placeholder="Enter notes"
            name={`entries.${index}.financialNotes`}
            type="text"
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2">
          <FormInput
            form={form}
            label="Freight Class"
            placeholder="Enter freight class"
            name={`entries.${index}.freightClass`}
            type="text"
          />
          <FormInput
            form={form}
            label="Weight Unit"
            placeholder="Enter weight unit"
            name={`entries.${index}.weightUnitName`}
            type="text"
          />
          <FormInput
            form={form}
            label="Quantity Billed"
            placeholder="Enter quantity billed"
            name={`entries.${index}.quantityBilledText`}
            type="text"
          />
          <FormInput
            form={form}
            label="Quantity Shipped"
            placeholder="Enter quantity shipped"
            name={`entries.${index}.qtyShipped`}
            type="number"
          />
        </div>
        {/* New row for Invoice Type and Invoice Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 mt-2">
          <SearchSelect
            form={form}
            name={`entries.${index}.invoiceType`}
            label="Invoice Type"
            placeholder="Search Invoice Type"
            isRequired
            options={[
              { value: "FREIGHT", label: "FREIGHT" },
              { value: "ADDITIONAL", label: "ADDITIONAL" },
              { value: "BALANCED DUE", label: "BALANCED DUE" },
              { value: "CREDIT", label: "CREDIT" },
              { value: "REVISED", label: "REVISED" },
            ]}
          />
          <FormInput
            className="mt-2"
            form={form}
            label="Invoice Status"
            name={`entries.${index}.invoiceStatus`}
            type="text"
            disable={true}
          />
          <div></div>
          <div></div>
        </div>
      </div>

      {/* Additional Information & Documents Section */}
      <div className="mt-4">
        <div className="flex items-center space-x-2 mb-2">
          <Info className="w-4 h-4 text-gray-600" />
          <h3 className="text-sm font-semibold text-gray-900">
            Additional Information
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
          <FormInput
            form={form}
            label="Notes (Remarks)"
            name={`entries.${index}.notes`}
            type="text"
          />
          <FormCheckboxGroup
            form={form}
            label="Documents Available"
            name={`entries.${index}.docAvailable`}
            options={[
              { label: "Invoice", value: "Invoice" },
              { label: "BOL", value: "Bol" },
              { label: "POD", value: "Pod" },
              { label: "Packages List", value: "Packages List" },
              { label: "Other Documents", value: "Other Documents" },
            ]}
            className="flex-row gap-2 text-xs"
          />
          {/* Conditional input field for Other Documents */}
          {(() => {
            const docAvailable = entry?.docAvailable || [];
            const hasOtherDocuments = docAvailable.includes("Other Documents");
            return hasOtherDocuments ? (
              <FormInput
                form={form}
                label="Specify Other Documents"
                name={`entries.${index}.otherDocuments`}
                type="text"
                isRequired
                placeholder="Enter other document types..."
              />
            ) : (
              <div></div>
            );
          })()}
        </div>
      </div>

      {/* Custom Fields Section */}
      {Array.isArray(customFields) && customFields.length > 0 && (
        <div className="pt-3 border-t border-gray-100 mt-4">
          <div className="flex items-center space-x-2 mb-2">
            <Hash className="w-4 h-4 text-purple-600" />
            <h3 className="text-sm font-semibold text-gray-900">
              Custom Fields ({customFields.length})
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
            {customFields.map((cf: any, cfIdx: number) => {
              const fieldType = cf.type || "TEXT";
              const isAutoField = fieldType === "AUTO";
              const autoOption = cf.autoOption;
              const isDateField =
                fieldType === "DATE" || (isAutoField && autoOption === "DATE");
              let inputType = "text";
              if (isDateField) inputType = "date";
              else if (fieldType === "NUMBER") inputType = "number";
              const fieldLabel = isAutoField
                ? `${cf.name} (Auto - ${autoOption})`
                : cf.name;
              return isDateField ? (
                <FormDatePicker
                  key={cf.id}
                  form={form}
                  label={fieldLabel}
                  name={`entries.${index}.customFields.${cfIdx}.value`}
                  className="w-full"
                  disable={isAutoField}
                  placeholder="DD/MM/YYYY"
                />
              ) : (
                <FormInput
                  key={cf.id}
                  form={form}
                  label={fieldLabel}
                  name={`entries.${index}.customFields.${cfIdx}.value`}
                  type={inputType}
                  className="w-full"
                  disable={isAutoField}
                />
              );
            })}
          </div>
        </div>
      )}

      {/* Entry-level error display (optional) */}
      {/* You can add error display here if needed */}

      {/* Entry-level actions placeholder (delete/duplicate) */}
      {/* <div className="flex justify-end mt-2">
        <button className="btn btn-danger">Delete</button>
      </div> */}

      {/* Entry Actions - Filename Status Tooltip */}
      <div className="pt-3 border-t border-gray-100 mt-3">
        <div className="flex items-center justify-end space-x-2">
          <Tooltip>
            <TooltipTrigger asChild tabIndex={-1}>
              <div
                className={`w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 ${
                  !clientFilePathFormat
                    ? "bg-gray-400 hover:bg-gray-500"
                    : generatedFilenames[index] && filenameValidation[index]
                    ? "bg-green-500 hover:bg-green-600"
                    : "bg-orange-500 hover:bg-orange-600"
                }`}
                tabIndex={-1}
                role="button"
                aria-label={`Entry ${index + 1} filename status`}
              >
                !
              </div>
            </TooltipTrigger>
            <TooltipContent side="top" align="center" className="z-[9999]">
              {renderTooltipContent(index)}
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default TracksheetEntryForm;
